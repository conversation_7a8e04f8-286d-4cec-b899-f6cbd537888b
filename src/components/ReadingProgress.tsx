import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiArrowUp } from 'react-icons/fi';
import { componentConfig } from '../config/components';

interface ReadingProgressProps {
  showAfter?: number; // Show progress bar after scrolling this many pixels
  showBackToTop?: boolean; // Show back to top button when progress is visible
  height?: number; // Height of the progress bar in pixels
  className?: string; // Additional CSS classes
  showPercentage?: boolean; // Show percentage indicator
  gradient?: string; // Custom gradient classes
}

export default function ReadingProgress({
  showAfter,
  showBackToTop,
  height,
  className = '',
  showPercentage,
  gradient
}: ReadingProgressProps) {
  const config = componentConfig.readingProgress;

  // Use props or fall back to config defaults
  const finalShowAfter = showAfter ?? config.showAfter;
  const finalShowBackToTop = showBackToTop ?? config.showBackToTop;
  const finalHeight = height ?? config.height;
  const finalShowPercentage = showPercentage ?? config.showPercentage;
  const finalGradient = gradient ?? config.gradient;
  const [isVisible, setIsVisible] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = docHeight > 0 ? (scrollTop / docHeight) * 100 : 0;

      setScrollProgress(Math.min(Math.max(progress, 0), 100));
      setIsVisible(scrollTop > finalShowAfter);
    };

    // Initial calculation
    handleScroll();

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [finalShowAfter]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Don't render if disabled in config or on server to prevent hydration mismatch
  if (!config.enabled || !mounted) {
    return null;
  }

  return (
    <>
      {/* Fixed Progress Bar at Top */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className={`fixed top-0 left-0 right-0 z-[60] ${className}`}
            style={{ willChange: 'transform' }}
          >
            {/* Background container with backdrop blur */}
            <div className="bg-white/80 dark:bg-neutral-900/80 backdrop-blur-md border-b border-neutral-200/50 dark:border-neutral-700/50">
              {/* Progress bar container */}
              <div className="relative w-full overflow-hidden" style={{ height: `${finalHeight}px` }}>
                {/* Background track */}
                <div className="absolute inset-0 bg-neutral-200/50 dark:bg-neutral-700/50" />
                
                {/* Progress fill with gradient */}
                <motion.div
                  className={`absolute top-0 left-0 h-full bg-gradient-to-r ${finalGradient}`}
                  style={{
                    width: `${scrollProgress}%`,
                    boxShadow: '0 0 10px rgba(59, 130, 246, 0.5)',
                  }}
                  initial={{ width: 0 }}
                  animate={{ width: `${scrollProgress}%` }}
                  transition={{ duration: 0.1, ease: 'easeOut' }}
                />
                
                {/* Animated shimmer effect */}
                <motion.div
                  className="absolute top-0 left-0 h-full w-20 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                  animate={{
                    x: [-80, window.innerWidth + 80],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'linear',
                    repeatDelay: 3,
                  }}
                  style={{
                    display: config.animation.shimmer && scrollProgress > 0 && scrollProgress < 100 ? 'block' : 'none',
                  }}
                />
              </div>
              
              {/* Progress percentage indicator */}
              {finalShowPercentage && (
                <div className="absolute top-full right-4 mt-1">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="bg-white/90 dark:bg-neutral-800/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium text-neutral-600 dark:text-neutral-400 border border-neutral-200/50 dark:border-neutral-700/50 shadow-sm"
                  >
                    {Math.round(scrollProgress)}%
                  </motion.div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Back to Top Button - positioned at top right when progress is visible */}
      <AnimatePresence>
        {isVisible && finalShowBackToTop && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, x: 20 }}
            animate={{ opacity: 1, scale: 1, x: 0 }}
            exit={{ opacity: 0, scale: 0.8, x: 20 }}
            transition={{ duration: 0.3, ease: 'easeOut', delay: 0.1 }}
            className="fixed top-6 right-4 z-[60]"
            style={{ willChange: 'transform' }}
          >
            <motion.button
              onClick={scrollToTop}
              className="group flex items-center justify-center w-10 h-10 bg-white/90 dark:bg-neutral-800/90 hover:bg-white dark:hover:bg-neutral-700 border border-neutral-200/50 dark:border-neutral-700/50 rounded-full shadow-lg hover:shadow-xl backdrop-blur-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              aria-label={`Back to top (${Math.round(scrollProgress)}% read)`}
              title={`Back to top - ${Math.round(scrollProgress)}% read`}
            >
              <FiArrowUp className="w-4 h-4 text-neutral-600 dark:text-neutral-400 group-hover:text-blue-500 transition-colors" />
              
              {/* Tooltip */}
              <span className="absolute top-full right-0 mt-2 px-2 py-1 bg-neutral-800 dark:bg-neutral-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
                Back to top ({Math.round(scrollProgress)}%)
              </span>
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
