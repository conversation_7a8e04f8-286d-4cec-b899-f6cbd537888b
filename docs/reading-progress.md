# Reading Progress Indicator

The Reading Progress Indicator is a modern UI component that displays reading progress at the top of the page and provides a convenient back-to-top button.

## Features

- **Top Progress Bar**: Minimal horizontal progress bar fixed at the top of the page (no background track)
- **Smooth Animations**: Framer Motion powered animations with shimmer effects
- **Back to Top Button**: Circular button positioned at bottom-right corner with progress ring
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Theme Support**: Adapts to light/dark theme automatically
- **Configurable**: Fully customizable through configuration file

## Configuration

The reading progress indicator can be configured in `src/config/components.ts`:

```typescript
readingProgress: {
  enabled: true,                    // Enable/disable the component
  showAfter: 200,                   // Show after scrolling this many pixels
  height: 4,                        // Height of progress bar in pixels
  showBackToTop: true,              // Show back to top button
  gradient: 'from-blue-500 via-purple-500 to-blue-600', // Tailwind gradient
  animation: {
    shimmer: true,                  // Enable shimmer effect
    duration: 0.3,                  // Animation duration in seconds
  }
}
```

## Usage

### Basic Usage

```tsx
import ReadingProgress from './ReadingProgress';

// Use with default configuration
<ReadingProgress />
```

### Custom Configuration

```tsx
// Override specific properties
<ReadingProgress
  showAfter={300}
  height={5}
  showBackToTop={false}
  gradient="from-green-500 to-blue-500"
/>
```

## Component Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `showAfter` | `number` | `200` | Pixels to scroll before showing |
| `showBackToTop` | `boolean` | `true` | Show back to top button |
| `height` | `number` | `4` | Progress bar height in pixels |
| `gradient` | `string` | `'from-blue-500 via-purple-500 to-blue-600'` | Tailwind gradient classes |
| `className` | `string` | `''` | Additional CSS classes |

## Design Features

### Progress Bar
- Fixed positioning at the very top of the page (`top-0`)
- Minimal design with no background track (only colored progress fill visible)
- Smooth gradient animation with configurable colors
- Optional shimmer effect for enhanced visual appeal

### Back to Top Button
- Positioned at bottom-right corner (original position)
- Circular design with progress ring indicator
- Simple tooltip without percentage display
- Accessible with proper ARIA labels

### Responsive Behavior
- Progress bar adapts to all screen sizes
- Clean minimal appearance without background clutter
- Touch-friendly button sizing on mobile devices

## Integration

The component is automatically integrated into:
- Blog post pages (`BlogPostLayout.tsx`)
- Blog listing pages (`BlogPageClient.tsx`)
- Archive pages (`ArchiveClient.tsx`)
- Series pages (`SeriesPageClient.tsx`, `SeriesDetailClient.tsx`)

## Layout Considerations

### Header Positioning
The main header has been adjusted from `top-4` to `top-6` to accommodate the progress bar.

### Table of Contents
The scroll offset for table of contents navigation has been increased from 100px to 110px to account for the progress bar.

### Z-Index Hierarchy
- Reading Progress: `z-[60]`
- Header: `z-40`
- Other floating elements: `z-50` or lower

## Accessibility

- Proper ARIA labels for screen readers
- Keyboard navigation support
- High contrast colors for visibility
- Smooth scroll behavior for better UX

## Performance

- Uses `passive: true` scroll listeners for better performance
- Optimized with `willChange: transform` for smooth animations
- Debounced scroll calculations to prevent excessive updates
- Conditional rendering to avoid unnecessary DOM updates

## Browser Support

- Modern browsers with CSS Grid and Flexbox support
- Backdrop filter support (graceful degradation on older browsers)
- Framer Motion animation support
- CSS custom properties support
